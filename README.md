# 星图 (CloudDrive) 项目大纲

## 1. 项目概述

### 项目名称
**星图 (CloudDrive)** - 现代化云盘桌面应用程序

### 主要功能描述
- **文件管理**: 支持文件/文件夹的上传、下载、预览、重命名、移动、删除等操作
- **智能上传**: 基于TUS协议的断点续传上传，支持大文件和批量文件智能打包
- **流式下载**: 支持大文件流式下载和断点续传
- **7z压缩解压**: 自动压缩打包和解压缩功能
- **文件预览**: 支持多种文件类型的在线预览
- **标签管理**: 文件标签分类和管理系统
- **权限管理**: 用户权限和访问控制
- **回收站**: 文件删除和恢复功能
- **自动更新**: 应用程序自动更新机制

### 技术特点
- **跨平台**: 支持 Windows 和 macOS
- **现代化UI**: 基于 shadcn-vue 组件库的现代化界面
- **高性能**: 支持大文件处理和并发操作
- **类型安全**: 完整的 TypeScript 类型支持
- **模块化**: 清晰的模块分离和组件化架构

## 2. 技术架构

### 前端技术栈
- **框架**: Vue 3 (Composition API)
- **语言**: TypeScript
- **UI库**: shadcn-vue + Radix Vue + Tailwind CSS
- **图标**: Lucide Vue Next
- **状态管理**: Pinia + Pinia Plugin Persistedstate
- **路由**: Vue Router 4
- **工具库**: @vueuse/core, clsx, class-variance-authority

### 桌面应用框架
- **主框架**: Electron 28.0.0
- **日志系统**: electron-log
- **数据存储**: electron-store
- **自动更新**: electron-updater

### 构建和开发工具
- **构建工具**: Vite 5.0.10
- **Electron插件**: vite-plugin-electron, vite-plugin-electron-renderer
- **打包工具**: electron-builder
- **包管理器**: pnpm 10.11.1
- **类型检查**: vue-tsc
- **样式处理**: PostCSS + Autoprefixer

### 核心依赖库
- **HTTP客户端**: Axios
- **文件上传**: tus-js-client (TUS协议)
- **文件下载**: streamsaver
- **压缩解压**: node-7z + 7zip-bin
- **表格组件**: @tanstack/vue-table
- **通知系统**: vue-sonner

## 3. 项目结构

### 根目录结构
```
cloudDrive/
├── electron/                 # Electron主进程代码
├── src/                     # Vue前端源码
├── dist/                    # 前端构建输出
├── dist-electron/           # Electron构建输出
├── release/                 # 应用打包输出
├── public/                  # 静态资源
├── node_modules/            # 依赖包
├── package.json             # 项目配置
├── vite.config.ts          # Vite配置
├── electron-builder.yml    # Electron打包配置
├── tsconfig.json           # TypeScript配置
├── tailwind.config.js      # Tailwind CSS配置
└── components.json         # shadcn-vue配置
```

### Electron主进程结构 (`electron/`)
```
electron/
├── main.ts                 # 主进程入口
├── preload.ts             # 预加载脚本
├── tus/                   # TUS上传模块
├── stream-downloader/     # 流式下载模块
├── archive/               # 压缩模块
├── 7z-extractor/         # 解压缩模块
├── auto-updater/         # 自动更新模块
├── auth/                 # 认证模块
└── logger/               # 日志模块
```

### 前端源码结构 (`src/`)
```
src/
├── main.ts               # 应用入口
├── App.vue              # 根组件
├── api/                 # API服务层
├── components/          # 可复用组件
├── composables/         # 组合式函数
├── views/              # 页面组件
├── router/             # 路由配置
├── store/              # 状态管理
├── types/              # 类型定义
├── utils/              # 工具函数
├── lib/                # 库文件
├── config/             # 配置文件
└── style.css           # 全局样式
```

## 4. 核心功能模块

### 4.1 文件管理模块
- **位置**: `src/views/Folders/`, `src/composables/useDirectoryStructure.ts`
- **功能**: 文件夹导航、文件列表展示、文件操作
- **组件**: FolderView, FilesView, GridView, ListView

### 4.2 文件上传模块
- **位置**: `electron/tus/`, `src/components/Upload/`
- **功能**: TUS协议上传、断点续传、智能打包、拖拽上传
- **特性**: 支持大文件、批量上传、进度监控

### 4.3 文件下载模块
- **位置**: `electron/stream-downloader/`, `src/composables/useFileDownload.ts`
- **功能**: 流式下载、断点续传、批量下载
- **特性**: 支持大文件、进度监控、路径选择

### 4.4 压缩解压模块
- **位置**: `electron/archive/`, `electron/7z-extractor/`
- **功能**: 7z格式压缩和解压缩
- **特性**: 自动压缩、后台解压、进度监控

### 4.5 用户认证模块
- **位置**: `src/composables/useAuth.ts`, `src/views/Login.vue`
- **功能**: 用户登录、token管理、权限验证
- **特性**: iframe登录、自动token刷新

### 4.6 文件预览模块
- **位置**: `src/components/Preview/`
- **功能**: 多种文件类型预览
- **支持**: 图片、文档、视频等格式

## 5. 关键组件

### 5.1 Electron主进程组件

#### TUS上传管理器 (`electron/tus/uploadManager.ts`)
- 管理上传任务生命周期
- 处理断点续传逻辑
- 提供事件通知机制

#### 流式下载管理器 (`electron/stream-downloader/downloadManager.ts`)
- 管理下载任务队列
- 支持并发下载控制
- 处理下载进度和状态

#### 压缩管理器 (`electron/archive/archiveManager.ts`)
- 智能文件打包逻辑
- 临时文件生命周期管理
- 压缩进度监控

### 5.2 前端核心组件

#### 文件视图组件 (`src/views/Folders/FilesView/`)
- GridView: 网格视图展示
- ListView: 列表视图展示
- ContextMenu: 右键菜单
- DetailPanel: 文件详情面板

#### 上传组件 (`src/components/Upload/`)
- UploadDialog: 上传对话框
- ProgressIndicator: 上传进度指示器
- useTusUpload: 上传逻辑组合函数

#### UI组件库 (`src/components/ui/`)
- 基于 shadcn-vue 的可复用UI组件
- 包含按钮、对话框、表格、表单等组件

## 6. API接口

### 6.1 主要API端点

#### 文件管理API (`src/api/services/files.ts`)
- `POST /netdisk/startTask` - 开始上传任务
- `POST /netdisk/deleteFile` - 删除文件
- `POST /netdisk/moveFiles` - 移动文件
- `POST /netdisk/renameFile` - 重命名文件
- `GET /netdisk/getDownloadTasks` - 获取下载任务

#### 通用API (`src/api/services/common.ts`)
- `GET /auth/login` - 获取用户信息
- `GET /netdisk/getTopCategoryDirectories` - 获取顶级目录
- `POST /netdisk/getDirectoryContents` - 获取目录内容
- `GET /netdisk/getAllFilterOptions` - 获取筛选选项

#### 标签管理API (`src/api/services/tags.ts`)
- `GET /netdisk/getTagList` - 获取标签列表
- `POST /netdisk/editTag` - 编辑标签
- `GET /netdisk/getGameList` - 获取游戏项目列表

### 6.2 数据流
1. **上传流程**: 前端选择文件 → Electron TUS模块 → 后端TUS服务器 → 调用startTask同步
2. **下载流程**: 前端请求下载 → Electron下载模块 → 流式下载 → 本地保存
3. **文件管理**: 前端操作 → API调用 → 后端处理 → 前端状态更新

## 7. 开发和构建

### 7.1 开发环境设置
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev:electron

# 类型检查
pnpm type-check

# 代码检查
pnpm lint
```

### 7.2 构建流程
```bash
# 构建前端
pnpm build:web

# 构建Electron
pnpm build:electron

# 完整构建
pnpm build

# 清理构建文件
pnpm clean
```

### 7.3 环境配置
- **开发环境**: `.env.development`
- **生产环境**: `.env.production`
- **测试环境**: `.env.test`

主要环境变量：
- `VITE_API_BASE_URL`: API基础URL
- `VITE_TUS_ENDPOINT`: TUS上传端点
- `VITE_AUTH_URL`: 认证中心地址
- `VITE_DOWNLOAD_ENDPOINT`: 下载服务端点

## 8. 部署和打包

### 8.1 应用打包
- **Windows**: `pnpm build:win` → 生成 `.exe` 安装包
- **macOS**: `pnpm build:mac` → 生成 `.dmg` 安装包
- **测试版本**: `pnpm build:test:win/mac` → 生成测试版安装包

### 8.2 打包配置
- **主配置**: `electron-builder.yml`
- **测试配置**: `electron-builder.test.yml`
- **输出目录**: `release/` (生产版) 或 `release-test/` (测试版)

### 8.3 自动更新
- 基于 `electron-updater`
- 支持增量更新
- 自动检查和下载更新
- 用户确认后安装

### 8.4 分发方式
- **Windows**: NSIS安装程序，支持自定义安装路径
- **macOS**: DMG磁盘映像
- **自动更新**: 通过HTTP服务器分发更新包

---

*本文档基于项目当前状态生成，随着项目发展可能需要更新。*
