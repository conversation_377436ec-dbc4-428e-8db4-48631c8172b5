# 星图 (CloudDrive) 技术文档

## 1. 项目概述

### 项目名称
**星图 (CloudDrive)** - 现代化云盘桌面应用程序

### 项目简介
星图是一款基于 Electron + Vue 3 + TypeScript 构建的现代化云盘桌面应用程序，专为企业级文件管理和协作而设计。应用程序采用模块化架构，提供高性能的文件上传下载、智能压缩打包、实时预览等功能，支持跨平台部署。

### 核心功能特性

#### 🚀 智能文件管理
- **多视图展示**: 支持网格视图和列表视图，提供灵活的文件浏览体验
- **高级筛选**: 基于文件类型、大小、修改时间等多维度筛选
- **批量操作**: 支持文件的批量选择、移动、删除、重命名等操作
- **实时搜索**: 基于文件名和内容的快速搜索功能
- **文件预览**: 支持图片、文档、视频等多种格式的在线预览

#### 📤 智能上传系统
- **TUS协议**: 基于TUS (Tus Resumable Upload) 协议的断点续传上传
- **智能打包**: 10个文件即触发快速7z压缩，优化网络请求效率
- **拖拽上传**: 支持文件和文件夹的拖拽上传，保持目录结构
- **并发控制**: 智能的并发上传控制，最大化利用带宽
- **进度监控**: 实时显示上传进度、速度和剩余时间

#### 📥 高效下载系统
- **流式下载**: 基于 StreamSaver 的大文件流式下载
- **断点续传**: 支持下载中断后的断点续传功能
- **批量下载**: 支持多文件并发下载，智能队列管理
- **自动解压**: 下载完成后自动解压7z压缩包
- **路径选择**: 灵活的下载路径选择和管理

#### 🗜️ 智能压缩系统
- **7z压缩**: 基于7zip的高效压缩算法
- **自动触发**: 智能检测文件数量和大小，自动触发压缩
- **后台处理**: 压缩和解压缩在后台进程中执行，不阻塞UI
- **进度监控**: 实时显示压缩和解压缩进度

#### 🔐 安全认证系统
- **SSO集成**: 集成企业级单点登录系统
- **Token管理**: 自动token刷新和过期处理
- **权限控制**: 基于角色的文件访问权限控制
- **安全传输**: 所有数据传输采用HTTPS加密

#### 🏷️ 标签管理系统
- **分类标签**: 支持文件的多级分类标签
- **批量标记**: 支持批量添加和修改文件标签
- **标签筛选**: 基于标签的快速文件筛选和查找
- **游戏项目**: 专门的游戏项目管理功能

### 技术特点

#### 🎯 架构优势
- **模块化设计**: 清晰的模块分离，易于维护和扩展
- **类型安全**: 完整的 TypeScript 类型支持，减少运行时错误
- **响应式架构**: 基于 Vue 3 Composition API 的响应式数据管理
- **事件驱动**: 基于事件的组件通信和状态管理

#### ⚡ 性能优化
- **虚拟滚动**: 大量文件列表的虚拟滚动优化
- **懒加载**: 组件和资源的按需加载
- **内存管理**: 智能的内存使用和垃圾回收
- **并发控制**: 合理的并发请求控制，避免资源竞争

#### 🎨 用户体验
- **现代化UI**: 基于 shadcn-vue 的现代化界面设计
- **响应式布局**: 适配不同屏幕尺寸的响应式布局
- **主题支持**: 支持明暗主题切换
- **国际化**: 支持多语言界面（预留）

#### 🔧 开发体验
- **热重载**: 开发环境下的快速热重载
- **类型检查**: 实时的 TypeScript 类型检查
- **代码规范**: 统一的代码格式化和规范检查
- **调试支持**: 完善的调试工具和日志系统

## 2. 技术架构

### 2.1 整体架构设计

星图采用经典的 Electron 三层架构模式：

```
┌─────────────────────────────────────────────────────────────┐
│                    渲染进程 (Renderer Process)                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Vue 3 + TypeScript 前端应用              │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │ │
│  │  │   Views     │ │ Components  │ │ Composables │  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘  │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │ │
│  │  │   Router    │ │    Store    │ │     API     │  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                    IPC Communication
                              │
┌─────────────────────────────────────────────────────────────┐
│                     主进程 (Main Process)                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   核心业务模块                        │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │ │
│  │  │ TUS Upload  │ │  Download   │ │  Archive    │  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘  │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐  │ │
│  │  │ Extraction  │ │   Logger    │ │ Auto-Update │  │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                      System APIs & File System
                              │
┌─────────────────────────────────────────────────────────────┐
│                      操作系统层                             │
│           Windows / macOS / Linux (预留)                   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 前端技术栈详解

#### 核心框架
- **Vue 3.4.0**: 采用 Composition API，提供更好的类型推导和逻辑复用
  - 响应式系统：基于 Proxy 的响应式实现，性能更优
  - 组合式API：更好的逻辑组织和代码复用
  - Teleport：用于模态框和弹出层的渲染
  - Suspense：异步组件的加载状态管理

- **TypeScript 5.4.0**: 提供完整的类型安全保障
  - 严格模式配置，确保类型安全
  - 路径映射配置，简化模块导入
  - 声明文件管理，支持第三方库类型

#### UI 组件体系
- **shadcn-vue**: 现代化的无样式组件库
  - 基于 Radix Vue 的可访问性组件
  - 完全可定制的样式系统
  - TypeScript 原生支持

- **Tailwind CSS**: 原子化CSS框架
  - 响应式设计支持
  - 暗色主题配置
  - 自定义设计令牌

- **Lucide Vue Next**: 现代化图标库
  - 一致的设计风格
  - 可定制的图标属性
  - 树摇优化支持

#### 状态管理
- **Pinia**: Vue 3 官方推荐的状态管理库
  - 模块化的 store 设计
  - TypeScript 原生支持
  - 开发工具集成

- **Pinia Plugin Persistedstate**: 状态持久化插件
  - 自动状态持久化
  - 选择性持久化配置
  - 多存储后端支持

#### 路由系统
- **Vue Router 4**: Vue 3 配套的路由系统
  - 基于 Promise 的导航守卫
  - 动态路由匹配
  - 嵌套路由支持

### 2.3 Electron 架构详解

#### 主进程架构
- **Electron 28.0.0**: 基于 Chromium 和 Node.js
  - 窗口管理和生命周期控制
  - 系统API访问和文件系统操作
  - IPC通信协调

- **模块化设计**: 每个功能模块独立封装
  ```typescript
  // 模块初始化模式
  interface ModuleInterface {
    initialize(config: ModuleConfig): Promise<void>;
    cleanup(): Promise<void>;
    getStatus(): ModuleStatus;
  }
  ```

#### 预加载脚本
- **安全的API暴露**: 通过 contextBridge 安全地暴露API
- **类型安全**: 完整的TypeScript类型定义
- **事件系统**: 统一的事件监听和处理机制

### 2.4 构建和开发工具链

#### 构建系统
- **Vite 5.0.10**: 现代化的构建工具
  - 基于 ESBuild 的快速构建
  - 热模块替换 (HMR)
  - 代码分割和懒加载

- **vite-plugin-electron**: Electron 集成插件
  - 主进程和渲染进程的统一构建
  - 开发环境的自动重启
  - 源码映射支持

#### 包管理
- **pnpm 10.11.1**: 高效的包管理器
  - 硬链接节省磁盘空间
  - 严格的依赖管理
  - 工作区支持

#### 代码质量
- **vue-tsc**: Vue 组件的类型检查
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化

### 2.5 核心依赖库详解

#### 网络通信
- **Axios 1.9.0**: HTTP 客户端
  - 请求/响应拦截器
  - 自动错误处理
  - 请求取消支持

- **tus-js-client 4.3.1**: 断点续传上传
  - TUS 协议实现
  - 自动重试机制
  - 进度回调支持

#### 文件处理
- **StreamSaver 2.0.6**: 大文件流式保存
  - 突破浏览器内存限制
  - 支持大文件下载
  - 进度监控

- **node-7z 3.0.0**: 7z 压缩库
  - 高压缩比
  - 多线程压缩
  - 进度回调

#### 系统集成
- **electron-log 5.4.1**: 日志系统
  - 多级别日志
  - 文件轮转
  - 远程日志支持

- **electron-store 8.1.0**: 数据持久化
  - 加密存储支持
  - 模式验证
  - 迁移支持

- **electron-updater 6.1.7**: 自动更新
  - 增量更新
  - 签名验证
  - 回滚支持

## 3. 项目结构详解

### 3.1 根目录结构
```
cloudDrive/
├── electron/                 # Electron主进程代码
│   ├── main.ts              # 主进程入口文件
│   ├── preload.ts           # 预加载脚本
│   ├── tus/                 # TUS上传模块
│   ├── stream-downloader/   # 流式下载模块
│   ├── archive/             # 压缩模块
│   ├── 7z-extractor/       # 解压缩模块
│   ├── auto-updater/       # 自动更新模块
│   ├── auth/               # 认证模块
│   └── logger/             # 日志模块
├── src/                     # Vue前端源码
│   ├── main.ts             # 前端应用入口
│   ├── App.vue             # 根组件
│   ├── api/                # API服务层
│   ├── components/         # 可复用组件
│   ├── composables/        # 组合式函数
│   ├── views/              # 页面组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── types/              # 类型定义
│   ├── utils/              # 工具函数
│   ├── lib/                # 库文件
│   ├── config/             # 配置文件
│   └── style.css           # 全局样式
├── dist/                    # 前端构建输出
├── dist-electron/           # Electron构建输出
├── release/                 # 应用打包输出
├── public/                  # 静态资源
├── node_modules/            # 依赖包
├── package.json             # 项目配置
├── vite.config.ts          # Vite配置
├── electron-builder.yml    # Electron打包配置
├── tsconfig.json           # TypeScript配置
├── tailwind.config.js      # Tailwind CSS配置
├── components.json         # shadcn-vue配置
├── .env.development        # 开发环境配置
├── .env.production         # 生产环境配置
└── .env.test              # 测试环境配置
```

### 3.2 Electron主进程详细结构

#### 主进程核心文件
```
electron/
├── main.ts                 # 主进程入口，负责应用生命周期管理
├── preload.ts             # 预加载脚本，安全的API暴露
└── worker.ts              # 后台工作进程（7z解压缩）
```

#### TUS上传模块 (`electron/tus/`)
```
tus/
├── index.ts               # 模块入口和初始化
├── types.ts               # 类型定义
├── uploadManager.ts       # 上传管理器核心逻辑
├── ipcHandlers.ts         # IPC处理器
├── preloadApi.ts          # 预加载API
├── logger.ts              # 模块专用日志
└── README.md              # 模块文档
```

#### 流式下载模块 (`electron/stream-downloader/`)
```
stream-downloader/
├── index.ts               # 模块入口
├── types.ts               # 类型定义
├── downloadManager.ts     # 下载管理器
├── ipcHandlers.ts         # IPC处理器
├── preloadApi.ts          # 预加载API
├── logger.ts              # 模块日志
└── utils.ts               # 工具函数
```

#### 压缩模块 (`electron/archive/`)
```
archive/
├── index.ts               # 模块入口
├── types.ts               # 类型定义
├── archiveManager.ts      # 压缩管理器
├── ipcHandlers.ts         # IPC处理器
├── preloadApi.ts          # 预加载API
├── logger.ts              # 模块日志
└── utils.ts               # 压缩工具函数
```

#### 解压缩模块 (`electron/7z-extractor/`)
```
7z-extractor/
├── index.ts               # 模块入口
├── types.ts               # 类型定义
├── extractionManager.ts   # 解压缩管理器
├── worker.ts              # 后台工作进程
├── ipcHandlers.ts         # IPC处理器
├── preloadApi.ts          # 预加载API
└── logger.ts              # 模块日志
```

### 3.3 前端源码详细结构

#### API服务层 (`src/api/`)
```
api/
├── index.ts               # API统一入口
├── http.ts                # HTTP客户端封装
├── services/              # API服务层
│   ├── common.ts          # 通用API
│   ├── files.ts           # 文件管理API
│   └── tags.ts            # 标签管理API
└── README.md              # API使用文档
```

#### 组件系统 (`src/components/`)
```
components/
├── ui/                    # 基础UI组件（shadcn-vue）
│   ├── button/
│   ├── dialog/
│   ├── table/
│   └── ...
├── Upload/                # 上传相关组件
│   ├── UploadDialog.vue
│   ├── ProgressIndicator.vue
│   └── composables/
├── Preview/               # 文件预览组件
│   ├── FilePreviewModal.vue
│   ├── ImagePreview.vue
│   └── VideoPreview.vue
├── FolderPicker/          # 文件夹选择器
├── GlobalProgressIndicator/ # 全局进度指示器
├── AutoUpdater/           # 自动更新组件
├── Navigation.vue         # 导航组件
├── Sidebar.vue           # 侧边栏组件
└── ...
```

#### 组合式函数 (`src/composables/`)
```
composables/
├── useAuth.ts             # 认证相关
├── useAuthGuard.ts        # 路由守卫
├── useDirectoryStructure.ts # 目录结构管理
├── useFileDownload.ts     # 文件下载
├── useFileMove.ts         # 文件移动
├── useFileDelete.ts       # 文件删除
├── useDragAndDrop.ts      # 拖拽功能
├── useStreamDownload.ts   # 流式下载
├── useGlobalProgress.ts   # 全局进度管理
├── useViewMode.ts         # 视图模式
├── useSearch.ts           # 搜索功能
└── ...
```

#### 视图组件 (`src/views/`)
```
views/
├── Login.vue              # 登录页面
├── Resources.vue          # 资源首页
├── Folders/               # 文件夹相关视图
│   ├── FolderView.vue     # 文件夹主视图
│   ├── FilesView/         # 文件列表视图
│   │   ├── index.vue      # 文件视图主组件
│   │   ├── GridView.vue   # 网格视图
│   │   ├── ListView.vue   # 列表视图
│   │   ├── ContextMenu.vue # 右键菜单
│   │   └── Pagination.vue  # 分页组件
│   ├── DetailPanel/       # 详情面板
│   ├── FilterList.vue     # 筛选列表
│   └── ToolsBar/          # 工具栏
├── Tags/                  # 标签管理
│   └── index.vue
├── Trash/                 # 回收站
│   └── index.vue
└── Permissions.vue        # 权限管理
```

#### 状态管理 (`src/store/`)
```
store/
├── user.ts                # 用户状态
├── sidebar.ts             # 侧边栏状态
├── upload.ts              # 上传状态（如需要）
└── download.ts            # 下载状态（如需要）
```

#### 类型定义 (`src/types/`)
```
types/
├── electron.d.ts          # Electron API类型
├── env.d.ts               # 环境变量类型
├── files.ts               # 文件相关类型
├── user.ts                # 用户相关类型
└── api.ts                 # API响应类型
```

## 4. 核心功能模块详解

### 4.1 文件上传模块 - TUS协议实现

#### 4.1.1 模块架构
文件上传模块基于 TUS (Tus Resumable Upload) 协议实现，提供可靠的断点续传上传功能。

```typescript
// 核心类结构
class TusUploadManager extends EventEmitter {
  private tasks: Map<string, UploadTask> = new Map();
  private config: TusUploadConfig;
  private archiveManager?: ArchiveManager;

  // 核心方法
  async createUploadTask(filePath: string, metadata?: Record<string, string>): Promise<string>
  async startUpload(taskId: string): Promise<void>
  async pauseUpload(taskId: string): Promise<void>
  async resumeUpload(taskId: string): Promise<void>
  async cancelUpload(taskId: string): Promise<void>
}
```

#### 4.1.2 TUS协议实现细节

**协议流程**:
1. **创建上传会话**: 向服务器发送 POST 请求创建上传会话
2. **获取上传URL**: 服务器返回专用的上传URL
3. **分片上传**: 将文件分割成固定大小的分片进行上传
4. **断点续传**: 上传中断后可从上次位置继续
5. **完成验证**: 所有分片上传完成后进行完整性验证

**关键配置**:
```typescript
interface TusUploadConfig {
  endpoint: string;              // TUS服务器端点
  chunkSize: number;            // 分片大小 (默认20MB)
  retryDelays: number[];        // 重试延迟策略 [0, 1000, 3000, 5000]
  parallelUploads: number;      // 并行上传数量 (默认5)
  metadata: Record<string, string>; // 默认元数据
  headers: Record<string, string>;  // 默认请求头
}
```

#### 4.1.3 智能打包逻辑

**触发条件**:
- 文件数量 ≥ 10 个文件时自动触发
- 支持用户手动选择是否打包
- 排除已压缩文件（.zip, .rar, .7z等）

**打包流程**:
```typescript
// 智能打包决策算法
class SmartPackingDecision {
  shouldPack(files: File[]): boolean {
    const fileCount = files.length;
    const threshold = config.smartPacking.threshold; // 默认10

    if (fileCount < threshold) return false;

    // 排除已压缩文件
    const compressedExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz'];
    const uncompressedFiles = files.filter(file =>
      !compressedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
    );

    return uncompressedFiles.length >= threshold;
  }
}
```

**压缩策略**:
- 使用7z格式，压缩级别为0（最快压缩速度，优先用户体验）
- 保持原始目录结构
- 生成临时压缩文件，上传完成后自动清理

**配置优化说明**:
```typescript
// 智能打包配置优化 - 针对用户体验的平衡设计
const optimizedConfig = {
  // 阈值设为10：更早触发打包，减少HTTP请求数量
  threshold: 10,

  // 压缩级别0：最快压缩速度，减少用户等待时间
  // 在大量小文件场景下，减少请求数量的性能提升
  // 远大于压缩率提升带来的传输优化
  compressionLevel: 0,

  // 适用场景：
  // - 频繁上传大量小文件（如代码项目、文档集合）
  // - 网络延迟较高的环境（减少请求数量更重要）
  // - 用户对上传速度敏感的场景
};
```

#### 4.1.4 拖拽上传实现

**拖拽处理流程**:
```typescript
// 拖拽事件处理
const useDragAndDrop = (options: DragDropOptions, callbacks: DragDropCallbacks) => {
  const handleDrop = async (event: DragEvent) => {
    event.preventDefault();

    // 1. 获取拖拽的文件和文件夹
    const items = Array.from(event.dataTransfer?.items || []);
    const files: File[] = [];

    // 2. 递归处理文件夹
    for (const item of items) {
      if (item.kind === 'file') {
        const entry = item.webkitGetAsEntry();
        if (entry) {
          await processEntry(entry, files);
        }
      }
    }

    // 3. 智能打包检查
    if (shouldTriggerSmartPack(files)) {
      callbacks.onSmartPackTriggered?.(files, files.length);
    } else {
      callbacks.onFilesProcessed?.({ files: files.map(f => ({ file: f })) });
    }
  };
};
```

#### 4.1.5 进度监控和状态管理

**任务状态定义**:
```typescript
type UploadStatus =
  | 'pending'     // 等待上传
  | 'uploading'   // 上传中
  | 'paused'      // 已暂停
  | 'completed'   // 已完成
  | 'failed'      // 上传失败
  | 'cancelled';  // 已取消
```

**进度计算**:
```typescript
interface UploadProgress {
  taskId: string;
  fileName: string;
  bytesUploaded: number;
  bytesTotal: number;
  percentage: number;
  speed: number;           // 上传速度 (bytes/s)
  remainingTime: number;   // 预估剩余时间 (seconds)
  status: UploadStatus;
}
```

**事件系统**:
```typescript
// 上传管理器事件
uploadManager.on('task-created', (taskId: string, task: UploadTask) => {});
uploadManager.on('task-progress', (taskId: string, progress: UploadProgress) => {});
uploadManager.on('task-completed', (taskId: string) => {});
uploadManager.on('task-failed', (taskId: string, error: Error) => {});
```

### 4.2 文件下载模块 - 流式下载系统

#### 4.2.1 流式下载原理

**StreamSaver技术**:
- 利用 Service Worker 拦截网络请求
- 通过 ReadableStream 实现流式数据处理
- 突破浏览器内存限制，支持任意大小文件下载

**核心实现**:
```typescript
class StreamDownloadManager extends EventEmitter {
  private tasks: Map<string, DownloadTask> = new Map();
  private concurrentLimit: number = 10;
  private activeDownloads: Set<string> = new Set();

  async startDownload(task: DownloadTask): Promise<void> {
    // 1. 检查并发限制
    if (this.activeDownloads.size >= this.concurrentLimit) {
      await this.waitForSlot();
    }

    // 2. 创建流式下载
    const response = await fetch(task.downloadUrl, {
      headers: this.buildHeaders(task)
    });

    // 3. 创建可写流
    const fileStream = streamSaver.createWriteStream(task.fileName, {
      size: task.fileSize
    });

    // 4. 管道连接，开始下载
    const readableStream = new ReadableStream({
      start(controller) {
        const reader = response.body?.getReader();
        return pump();

        function pump(): Promise<void> {
          return reader!.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            // 更新进度
            task.bytesDownloaded += value.byteLength;
            this.emitProgress(task);

            controller.enqueue(value);
            return pump();
          });
        }
      }
    });

    readableStream.pipeTo(fileStream);
  }
}
```

#### 4.2.2 断点续传实现

**Range请求支持**:
```typescript
// 构建Range请求头
private buildHeaders(task: DownloadTask): Record<string, string> {
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${this.getAuthToken()}`
  };

  // 断点续传：添加Range头
  if (task.bytesDownloaded > 0) {
    headers['Range'] = `bytes=${task.bytesDownloaded}-`;
  }

  return headers;
}

// 恢复下载
async resumeDownload(taskId: string): Promise<void> {
  const task = this.tasks.get(taskId);
  if (!task) throw new Error('Task not found');

  // 检查本地文件大小
  const localFileSize = await this.getLocalFileSize(task.savePath);
  task.bytesDownloaded = localFileSize;

  // 重新开始下载
  await this.startDownload(task);
}
```

#### 4.2.3 批量下载管理

**队列管理**:
```typescript
class DownloadQueue {
  private queue: DownloadTask[] = [];
  private processing: boolean = false;

  async addTasks(tasks: DownloadTask[]): Promise<void> {
    this.queue.push(...tasks);
    if (!this.processing) {
      await this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.concurrentLimit);

      // 并发下载
      await Promise.allSettled(
        batch.map(task => this.downloadManager.startDownload(task))
      );
    }

    this.processing = false;
  }
}
```

#### 4.2.4 自动解压缩集成

**下载完成后处理**:
```typescript
// 下载完成事件处理
downloadManager.on('task-completed', async (taskId: string) => {
  const task = this.tasks.get(taskId);
  if (!task) return;

  // 检查是否需要解压缩
  if (this.shouldExtract(task.fileName)) {
    const extractionTask = await this.extractionManager.createExtractionTask({
      archivePath: task.savePath,
      extractPath: path.dirname(task.savePath),
      deleteAfterExtraction: true
    });

    await this.extractionManager.startExtraction(extractionTask.id);
  }
});

private shouldExtract(fileName: string): boolean {
  const extractableExtensions = ['.7z', '.zip', '.rar'];
  return extractableExtensions.some(ext =>
    fileName.toLowerCase().endsWith(ext)
  );
}
```

### 4.3 压缩解压模块 - 7z处理系统

#### 4.3.1 7z压缩算法选择

**压缩配置**:
```typescript
interface CompressionConfig {
  method: '7z' | 'zip';           // 压缩格式
  level: 0 | 1 | 3 | 5 | 7 | 9;   // 压缩级别
  threads: number;                // 线程数
  dictionary: string;             // 字典大小
  wordSize: number;               // 词汇大小
}

// 默认配置 - 优先压缩速度
const defaultConfig: CompressionConfig = {
  method: '7z',
  level: 0,        // 最快压缩速度，减少等待时间
  threads: Math.min(4, os.cpus().length), // 最多4线程
  dictionary: '32m', // 32MB字典
  wordSize: 32
};
```

**压缩命令构建**:
```typescript
private buildCompressionCommand(
  inputPaths: string[],
  outputPath: string,
  config: CompressionConfig
): string[] {
  return [
    '7z',
    'a',                          // 添加到压缩包
    '-t7z',                       // 7z格式
    `-mx=${config.level}`,        // 压缩级别
    `-mmt=${config.threads}`,     // 线程数
    `-md=${config.dictionary}`,   // 字典大小
    `-mfb=${config.wordSize}`,    // 词汇大小
    '-y',                         // 自动确认
    outputPath,
    ...inputPaths
  ];
}
```

#### 4.3.2 自动压缩触发条件

**智能压缩决策**:
```typescript
class AutoCompressionDecision {
  shouldCompress(files: FileInfo[]): boolean {
    // 1. 文件数量检查
    if (files.length < 10) return false;

    // 2. 总大小检查 - 避免压缩已经很大的单个文件
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const averageSize = totalSize / files.length;

    // 如果平均文件大小超过100MB，不进行压缩
    if (averageSize > 100 * 1024 * 1024) return false;

    // 3. 文件类型检查 - 排除已压缩文件
    const compressedTypes = ['.zip', '.rar', '.7z', '.tar.gz', '.bz2'];
    const compressibleFiles = files.filter(file =>
      !compressedTypes.some(type => file.name.toLowerCase().endsWith(type))
    );

    return compressibleFiles.length >= 10;
  }

  estimateCompressionRatio(files: FileInfo[]): number {
    // 根据文件类型估算压缩比
    let totalSize = 0;
    let estimatedCompressedSize = 0;

    for (const file of files) {
      totalSize += file.size;

      // 根据文件扩展名估算压缩比
      const ratio = this.getCompressionRatio(file.extension);
      estimatedCompressedSize += file.size * ratio;
    }

    return estimatedCompressedSize / totalSize;
  }

  private getCompressionRatio(extension: string): number {
    const ratios: Record<string, number> = {
      '.txt': 0.3,    // 文本文件压缩比很高
      '.log': 0.3,
      '.js': 0.4,     // 代码文件
      '.css': 0.4,
      '.html': 0.4,
      '.json': 0.4,
      '.jpg': 0.95,   // 图片文件已压缩
      '.png': 0.9,
      '.mp4': 0.98,   // 视频文件已压缩
      '.mp3': 0.98,
      '.pdf': 0.8,    // PDF有一定压缩空间
      '.docx': 0.7,   // Office文档
      '.xlsx': 0.7,
      '.pptx': 0.7,
    };

    return ratios[extension.toLowerCase()] || 0.6; // 默认压缩比
  }
}
```

#### 4.3.3 解压缩工作流程

**后台工作进程**:
```typescript
// worker.ts - 解压缩工作进程
import { Worker } from 'worker_threads';
import { spawn } from 'child_process';

class ExtractionWorker {
  private worker: Worker;

  constructor() {
    this.worker = new Worker(__filename, {
      workerData: { isWorker: true }
    });
  }

  async extract(task: ExtractionTask): Promise<void> {
    return new Promise((resolve, reject) => {
      this.worker.postMessage({
        type: 'extract',
        task
      });

      this.worker.on('message', (message) => {
        switch (message.type) {
          case 'progress':
            this.emitProgress(task.id, message.progress);
            break;
          case 'completed':
            resolve();
            break;
          case 'error':
            reject(new Error(message.error));
            break;
        }
      });
    });
  }
}

// 工作进程中的解压缩逻辑
if (workerData?.isWorker) {
  parentPort?.on('message', async (message) => {
    if (message.type === 'extract') {
      const task = message.task as ExtractionTask;

      try {
        const process = spawn('7z', [
          'x',                    // 解压缩
          task.archivePath,       // 源文件
          `-o${task.extractPath}`, // 输出目录
          '-y'                    // 自动确认
        ]);

        // 监听进度输出
        process.stdout.on('data', (data) => {
          const output = data.toString();
          const progress = parseProgress(output);
          if (progress) {
            parentPort?.postMessage({
              type: 'progress',
              progress
            });
          }
        });

        process.on('close', (code) => {
          if (code === 0) {
            parentPort?.postMessage({ type: 'completed' });
          } else {
            parentPort?.postMessage({
              type: 'error',
              error: `Extraction failed with code ${code}`
            });
          }
        });

      } catch (error) {
        parentPort?.postMessage({
          type: 'error',
          error: error.message
        });
      }
    }
  });
}
```

**进度解析**:
```typescript
function parseProgress(output: string): number | null {
  // 解析7z输出中的进度信息
  const progressMatch = output.match(/(\d+)%/);
  if (progressMatch) {
    return parseInt(progressMatch[1]);
  }

  // 解析文件数量进度
  const fileMatch = output.match(/(\d+) files, (\d+) folders/);
  if (fileMatch) {
    // 基于文件数量估算进度
    return null; // 需要更复杂的逻辑
  }

  return null;
}
```

### 4.4 用户认证模块 - SSO集成系统

#### 4.4.1 认证架构设计

**iframe登录实现**:
```typescript
// 认证状态管理
interface AuthState {
  isLoggedIn: boolean;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

class AuthManager {
  private authState = ref<AuthState>({
    isLoggedIn: false,
    token: null,
    isLoading: false,
    error: null
  });

  // 初始化认证
  async initialize(): Promise<void> {
    // 1. 检查本地存储的token
    const storedToken = localStorage.getItem('auth_token');
    if (storedToken) {
      await this.validateToken(storedToken);
    }

    // 2. 监听认证中心消息
    this.setupAuthListener();
  }

  private setupAuthListener(): void {
    // 监听来自认证中心的token
    window.electronAPI?.onAuthToken((token: string) => {
      this.handleTokenReceived(token);
    });
  }

  private async handleTokenReceived(token: string): Promise<void> {
    try {
      // 验证token有效性
      await this.validateToken(token);

      // 保存token
      localStorage.setItem('auth_token', token);
      this.authState.value.token = token;
      this.authState.value.isLoggedIn = true;
      this.authState.value.error = null;

      // 跳转到主页面
      await router.push('/resources');
    } catch (error) {
      this.handleAuthError(error);
    }
  }
}
```

**Token自动刷新**:
```typescript
class TokenRefreshManager {
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly REFRESH_INTERVAL = 30 * 60 * 1000; // 30分钟

  startAutoRefresh(token: string): void {
    this.stopAutoRefresh();

    this.refreshTimer = setInterval(async () => {
      try {
        await this.refreshToken();
      } catch (error) {
        console.error('Token refresh failed:', error);
        // 刷新失败，重新登录
        this.authManager.logout();
      }
    }, this.REFRESH_INTERVAL);
  }

  private async refreshToken(): Promise<void> {
    const response = await api.auth.refreshToken();
    if (response.code === 0) {
      const newToken = response.data.token;
      localStorage.setItem('auth_token', newToken);
      this.authManager.updateToken(newToken);
    } else {
      throw new Error('Token refresh failed');
    }
  }
}
```

#### 4.4.2 权限控制系统

**路由守卫**:
```typescript
export function createRouteGuard() {
  return async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const { isLoggedIn, isLoading } = useAuth();

    // 等待认证状态初始化
    if (isLoading.value) {
      await new Promise(resolve => {
        const unwatch = watch(isLoading, (loading) => {
          if (!loading) {
            unwatch();
            resolve(void 0);
          }
        });
      });
    }

    // 检查路由权限
    if (to.meta.requiresAuth && !isLoggedIn.value) {
      return { name: 'Login' };
    }

    // 已登录用户访问登录页，重定向到首页
    if (to.name === 'Login' && isLoggedIn.value) {
      return { name: 'Resources' };
    }

    return true;
  };
}
```

### 4.5 文件预览模块 - 多格式预览系统

#### 4.5.1 预览架构

**支持的文件类型**:
```typescript
interface PreviewConfig {
  images: string[];     // 图片格式
  videos: string[];     // 视频格式
  documents: string[];  // 文档格式
  code: string[];       // 代码文件
  text: string[];       // 文本文件
}

const PREVIEW_CONFIG: PreviewConfig = {
  images: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
  videos: ['.mp4', '.webm', '.ogg', '.mov', '.avi'],
  documents: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
  code: ['.js', '.ts', '.vue', '.html', '.css', '.json', '.xml'],
  text: ['.txt', '.md', '.log', '.csv']
};
```

**预览组件架构**:
```typescript
// 预览组件工厂
class PreviewComponentFactory {
  static createPreviewComponent(fileType: string, fileUrl: string): Component {
    const extension = path.extname(fileType).toLowerCase();

    if (PREVIEW_CONFIG.images.includes(extension)) {
      return ImagePreview;
    } else if (PREVIEW_CONFIG.videos.includes(extension)) {
      return VideoPreview;
    } else if (PREVIEW_CONFIG.documents.includes(extension)) {
      return DocumentPreview;
    } else if (PREVIEW_CONFIG.code.includes(extension)) {
      return CodePreview;
    } else if (PREVIEW_CONFIG.text.includes(extension)) {
      return TextPreview;
    }

    return UnsupportedPreview;
  }
}
```

## 5. 关键组件详解

### 5.1 Electron主进程核心组件

#### 5.1.1 模块管理器 (`electron/main.ts`)

**应用生命周期管理**:
```typescript
class ApplicationManager {
  private modules: Map<string, ModuleInterface> = new Map();
  private mainWindow: BrowserWindow | null = null;

  async initialize(): Promise<void> {
    // 1. 初始化日志系统
    await this.initializeLogger();

    // 2. 创建主窗口
    this.mainWindow = await this.createMainWindow();

    // 3. 初始化各功能模块
    await this.initializeModules();

    // 4. 设置IPC通信
    this.setupIpcHandlers();

    // 5. 设置应用事件监听
    this.setupAppEventListeners();
  }

  private async initializeModules(): Promise<void> {
    const moduleConfigs = [
      { name: 'tus', factory: () => initializeTusModule(this.mainWindow!) },
      { name: 'download', factory: () => initializeDownloadModule(this.mainWindow!) },
      { name: 'archive', factory: () => initializeArchiveModule() },
      { name: 'extraction', factory: () => initializeExtractionModule() },
      { name: 'updater', factory: () => initializeAutoUpdater(this.mainWindow!) }
    ];

    for (const config of moduleConfigs) {
      try {
        const module = await config.factory();
        this.modules.set(config.name, module);
        logger.info(`✅ ${config.name} 模块初始化成功`);
      } catch (error) {
        logger.error(`❌ ${config.name} 模块初始化失败:`, error);
      }
    }
  }
}
```

#### 5.1.2 IPC通信管理器

**统一的IPC处理**:
```typescript
class IpcManager {
  private handlers: Map<string, IpcHandler> = new Map();

  registerHandler(channel: string, handler: IpcHandler): void {
    this.handlers.set(channel, handler);

    ipcMain.handle(channel, async (event, ...args) => {
      try {
        const result = await handler.handle(event, ...args);
        return { success: true, data: result };
      } catch (error) {
        logger.error(`IPC处理错误 [${channel}]:`, error);
        return { success: false, error: error.message };
      }
    });
  }

  // 批量注册处理器
  registerHandlers(handlers: Record<string, IpcHandler>): void {
    Object.entries(handlers).forEach(([channel, handler]) => {
      this.registerHandler(channel, handler);
    });
  }
}
```

#### 5.1.3 错误处理和恢复机制

**全局错误处理**:
```typescript
class ErrorManager {
  setupGlobalErrorHandlers(): void {
    // 未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常:', error);
      this.handleCriticalError(error);
    });

    // 未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', reason);
      this.handlePromiseRejection(reason, promise);
    });

    // Electron渲染进程崩溃
    app.on('render-process-gone', (event, webContents, details) => {
      logger.error('渲染进程崩溃:', details);
      this.handleRendererCrash(webContents, details);
    });
  }

  private handleCriticalError(error: Error): void {
    // 保存错误日志
    this.saveErrorReport(error);

    // 尝试优雅关闭
    this.gracefulShutdown();
  }

  private async gracefulShutdown(): Promise<void> {
    try {
      // 保存用户数据
      await this.saveUserData();

      // 清理临时文件
      await this.cleanupTempFiles();

      // 关闭所有模块
      await this.shutdownModules();

    } catch (error) {
      logger.error('优雅关闭失败:', error);
    } finally {
      app.quit();
    }
  }
}
```

### 5.2 前端核心组件详解

#### 5.2.1 文件视图组件系统

**虚拟滚动实现**:
```typescript
// GridView.vue - 网格视图虚拟滚动
<template>
  <div ref="containerRef" class="grid-container" @scroll="handleScroll">
    <div class="grid-content" :style="{ height: totalHeight + 'px' }">
      <div
        class="grid-items"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <FileGridItem
          v-for="item in visibleItems"
          :key="item.id"
          :item="item"
          :selected="selectedItems.has(item.id)"
          @click="handleItemClick"
          @double-click="handleItemDoubleClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const useVirtualScroll = (items: Ref<FileItem[]>, itemHeight: number) => {
  const containerRef = ref<HTMLElement>();
  const scrollTop = ref(0);
  const containerHeight = ref(0);

  // 计算可见项目
  const visibleItems = computed(() => {
    const startIndex = Math.floor(scrollTop.value / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight.value / itemHeight) + 1,
      items.value.length
    );

    return items.value.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index
    }));
  });

  // 计算偏移量
  const offsetY = computed(() => {
    const startIndex = Math.floor(scrollTop.value / itemHeight);
    return startIndex * itemHeight;
  });

  const totalHeight = computed(() => items.value.length * itemHeight);

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    scrollTop.value = target.scrollTop;
  };

  return {
    containerRef,
    visibleItems,
    offsetY,
    totalHeight,
    handleScroll
  };
};
</script>
```

#### 5.2.2 响应式数据管理

**文件状态管理**:
```typescript
// useFileState.ts - 文件状态管理
export function useFileState() {
  const files = ref<FileItem[]>([]);
  const selectedFiles = ref<Set<string>>(new Set());
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 文件操作
  const fileOperations = {
    async loadFiles(directoryId: string): Promise<void> {
      loading.value = true;
      error.value = null;

      try {
        const response = await api.common.getDirectoryContents({
          category_id: directoryId,
          id: directoryId,
          page: 1,
          limit: 1000
        });

        if (response.code === 0) {
          files.value = response.data.items;
        } else {
          throw new Error(response.msg);
        }
      } catch (err) {
        error.value = err instanceof Error ? err.message : '加载文件失败';
      } finally {
        loading.value = false;
      }
    },

    selectFile(fileId: string): void {
      selectedFiles.value.add(fileId);
    },

    deselectFile(fileId: string): void {
      selectedFiles.value.delete(fileId);
    },

    toggleSelection(fileId: string): void {
      if (selectedFiles.value.has(fileId)) {
        selectedFiles.value.delete(fileId);
      } else {
        selectedFiles.value.add(fileId);
      }
    },

    selectAll(): void {
      files.value.forEach(file => selectedFiles.value.add(file.id));
    },

    clearSelection(): void {
      selectedFiles.value.clear();
    }
  };

  return {
    files: readonly(files),
    selectedFiles: readonly(selectedFiles),
    loading: readonly(loading),
    error: readonly(error),
    ...fileOperations
  };
}
```

#### 5.2.3 性能优化组件

**防抖搜索组件**:
```typescript
// SearchInput.vue - 防抖搜索
<template>
  <div class="search-input">
    <Search class="search-icon" />
    <input
      v-model="searchQuery"
      type="text"
      placeholder="搜索文件..."
      class="search-field"
      @input="handleInput"
    />
    <button v-if="searchQuery" @click="clearSearch" class="clear-button">
      <X class="w-4 h-4" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { Search, X } from 'lucide-vue-next';

interface Props {
  modelValue: string;
  delay?: number;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'search', query: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  delay: 300
});

const emit = defineEmits<Emits>();

const searchQuery = ref(props.modelValue);

// 防抖搜索函数
const debouncedSearch = useDebounceFn((query: string) => {
  emit('search', query);
}, props.delay);

const handleInput = () => {
  emit('update:modelValue', searchQuery.value);
  debouncedSearch(searchQuery.value);
};

const clearSearch = () => {
  searchQuery.value = '';
  emit('update:modelValue', '');
  emit('search', '');
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue;
});
</script>
```

## 6. API接口详解

### 6.1 API架构设计

#### 6.1.1 HTTP客户端封装

**统一响应格式**:
```typescript
interface ApiResponse<T = any> {
  code: number;        // 状态码：0=成功，其他=失败
  msg: string;         // 响应消息
  data: T;            // 响应数据
  timestamp?: number;  // 时间戳
  requestId?: string;  // 请求ID
}

interface PaginatedResponse<T> extends ApiResponse<T> {
  data: {
    items: T[];        // 数据列表
    total: number;     // 总数量
    page: number;      // 当前页
    limit: number;     // 每页数量
    hasMore: boolean;  // 是否有更多数据
  };
}
```

**请求拦截器**:
```typescript
// 请求拦截器 - 添加认证和追踪
instance.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  // 1. 添加认证token
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // 2. 添加请求ID用于追踪
  config.headers['X-Request-ID'] = generateRequestId();

  // 3. 添加时间戳
  config.headers['X-Timestamp'] = Date.now().toString();

  // 4. 开发环境日志
  if (isDev) {
    console.log(`🚀 API请求 [${config.method?.toUpperCase()}] ${config.url}`, {
      params: config.params,
      data: config.data
    });
  }

  return config;
});
```

**响应拦截器**:
```typescript
// 响应拦截器 - 统一错误处理
instance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;

    // 业务状态检查
    if (data.code !== 0) {
      const error = new ApiError(data.msg, data.code);
      return Promise.reject(error);
    }

    return response;
  },
  (error: AxiosError) => {
    // HTTP状态码错误处理
    if (error.response) {
      const status = error.response.status;
      const message = getErrorMessage(status);

      // 401未授权 - 自动跳转登录
      if (status === 401) {
        clearAuthToken();
        router.push('/login');
      }

      return Promise.reject(new ApiError(message, status));
    }

    return Promise.reject(error);
  }
);
```

### 6.2 核心API接口详解

#### 6.2.1 文件管理API

**开始上传任务**:
```typescript
// POST /netdisk/startTask
interface StartTaskRequest {
  upload_url: string;           // TUS上传URL
  relative_path: string;        // 相对路径
  category_id: number | string; // 分类ID
  parent_id: number | string;   // 父目录ID
  is_folder?: number;          // 是否为文件夹 (1=是, 0=否)
  is_need_extract?: number;    // 是否需要解压 (1=是, 0=否)
  [key: string]: any;          // 自定义属性
}

interface StartTaskResponse {
  file_id: string;             // 文件ID
  file_path: string;           // 文件路径
  created_at: string;          // 创建时间
}

// 使用示例
const startUploadTask = async (taskData: StartTaskRequest): Promise<StartTaskResponse> => {
  const response = await api.files.startTask(taskData);
  return response.data;
};
```

**删除文件**:
```typescript
// POST /netdisk/deleteFile
interface DeleteFileRequest {
  category_id: number | string;
  file_ids: number | string | (number | string)[]; // 支持批量删除
}

interface DeleteFileResponse {
  deleted_count: number;       // 删除数量
  failed_files?: string[];     // 删除失败的文件
}

// 使用示例
const deleteFiles = async (categoryId: string, fileIds: string[]): Promise<void> => {
  await api.files.deleteFile({
    category_id: categoryId,
    file_ids: fileIds
  });
};
```

**移动文件**:
```typescript
// POST /netdisk/moveFiles
interface MoveFilesRequest {
  category_id: number | string;
  file_ids: number | string | (number | string)[];
  target_dir_id: number | string; // 目标目录ID
}

interface MoveFilesResponse {
  moved_count: number;         // 移动数量
  failed_files?: {             // 移动失败的文件
    file_id: string;
    reason: string;
  }[];
}
```

**重命名文件**:
```typescript
// POST /netdisk/renameFile
interface RenameFileRequest {
  category_id: number | string;
  file_id: number | string;
  new_name: string;            // 新文件名
}

interface RenameFileResponse {
  file_id: string;
  old_name: string;
  new_name: string;
  updated_at: string;
}
```

#### 6.2.2 目录和内容API

**获取目录内容**:
```typescript
// POST /netdisk/getDirectoryContents
interface DirectoryContentsRequest {
  category_id: number | string;
  id: number | string;         // 目录ID
  page: number | string;       // 页码
  limit: number | string;      // 每页数量

  // 筛选参数
  sortBy?: 'name' | 'size' | 'date' | 'type';
  sortOrder?: 'asc' | 'desc';
  fileTypes?: string[];        // 文件类型筛选
  sizeRange?: 'small' | 'medium' | 'large' | 'xlarge';
  search?: string;             // 搜索关键词

  // 自定义筛选
  [key: string]: any;
}

interface FileItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size: number;
  size_human: string;          // 人类可读的大小
  extension?: string;
  mime_type?: string;
  created_at: string;
  updated_at: string;
  parent_id: string;

  // 预览相关
  preview?: string;            // 预览URL
  thumbnail?: string;          // 缩略图URL

  // 下载相关
  download_url?: string;       // 下载URL

  // 自定义属性
  custom_fields?: Record<string, any>;

  // 权限相关
  permissions?: {
    read: boolean;
    write: boolean;
    delete: boolean;
  };
}

interface DirectoryContentsResponse {
  items: FileItem[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;

  // 目录信息
  directory: {
    id: string;
    name: string;
    path: string;
    parent_id?: string;
  };

  // 统计信息
  stats: {
    file_count: number;
    folder_count: number;
    total_size: number;
  };
}
```

**获取顶级目录**:
```typescript
// GET /netdisk/getTopCategoryDirectories
interface TopCategoryDirectory {
  id: string;
  name: string;
  code: string;               // 目录代码
  icon: string;               // 图标名称
  description?: string;       // 描述
  file_count: number;         // 文件数量
  total_size: number;         // 总大小
  permissions: {
    upload: boolean;
    download: boolean;
    delete: boolean;
  };
}

interface TopCategoryResponse {
  directories: TopCategoryDirectory[];
}
```

#### 6.2.3 筛选和搜索API

**获取筛选选项**:
```typescript
// GET /netdisk/getAllFilterOptions
interface FilterOption {
  key: string;                // 筛选键
  label: string;              // 显示标签
  type: 'select' | 'multiselect' | 'range' | 'date';
  options?: {                 // 选项列表
    value: string;
    label: string;
    count?: number;           // 该选项的文件数量
  }[];
  range?: {                   // 范围类型的配置
    min: number;
    max: number;
    step: number;
  };
}

interface FilterOptionsResponse {
  filters: FilterOption[];

  // 预定义筛选器
  predefined: {
    fileTypes: {
      images: string[];
      documents: string[];
      videos: string[];
      archives: string[];
    };
    sizeRanges: {
      small: { min: 0, max: 1024 * 1024 };      // < 1MB
      medium: { min: 1024 * 1024, max: 10 * 1024 * 1024 }; // 1-10MB
      large: { min: 10 * 1024 * 1024, max: 100 * 1024 * 1024 }; // 10-100MB
      xlarge: { min: 100 * 1024 * 1024, max: Infinity }; // > 100MB
    };
  };
}
```

#### 6.2.4 标签管理API

**获取标签列表**:
```typescript
// GET /netdisk/getTagList
interface Tag {
  id: string;
  name: string;
  color?: string;             // 标签颜色
  category_id: string;        // 所属分类
  file_count: number;         // 使用该标签的文件数量
  created_at: string;
  updated_at: string;
}

interface TagListResponse {
  tags: Tag[];
  categories: {               // 标签分类
    id: string;
    name: string;
    tags: Tag[];
  }[];
}
```

**编辑标签**:
```typescript
// POST /netdisk/editTag
interface EditTagRequest {
  category_id: number | string;
  tag_values: string[];       // 标签值数组
  file_ids?: string[];        // 要应用标签的文件ID（可选）
}

interface EditTagResponse {
  updated_tags: Tag[];
  affected_files: number;     // 受影响的文件数量
}
```

### 6.3 数据流和调用时序

#### 6.3.1 文件上传流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant E as Electron主进程
    participant T as TUS服务器
    participant B as 后端API

    U->>F: 选择文件上传
    F->>E: 创建上传任务
    E->>E: 检查是否需要智能打包

    alt 需要智能打包
        E->>E: 压缩文件
        E->>T: 上传压缩包
    else 直接上传
        E->>T: 分片上传文件
    end

    T-->>E: 上传进度回调
    E-->>F: 转发进度事件
    F-->>U: 显示上传进度

    T->>E: 上传完成
    E->>B: 调用startTask同步
    B-->>E: 返回文件信息
    E-->>F: 上传完成事件
    F-->>U: 显示上传成功
```

#### 6.3.2 文件下载流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant E as Electron主进程
    participant B as 后端API
    participant S as 文件服务器

    U->>F: 点击下载文件
    F->>E: 请求下载
    E->>B: 获取下载URL
    B-->>E: 返回下载URL

    E->>S: 开始流式下载
    S-->>E: 数据流
    E->>E: 写入本地文件
    E-->>F: 下载进度事件
    F-->>U: 显示下载进度

    S->>E: 下载完成

    alt 需要解压缩
        E->>E: 自动解压缩
        E-->>F: 解压进度事件
    end

    E-->>F: 下载完成事件
    F-->>U: 显示下载成功
```

#### 6.3.3 错误处理流程

**API错误处理策略**:
```typescript
class ApiErrorHandler {
  static handle(error: ApiError): void {
    switch (error.code) {
      case 401:
        // 未授权 - 跳转登录
        this.handleUnauthorized();
        break;
      case 403:
        // 禁止访问 - 显示权限错误
        this.handleForbidden(error.message);
        break;
      case 404:
        // 资源不存在
        this.handleNotFound(error.message);
        break;
      case 429:
        // 请求过于频繁 - 显示限流提示
        this.handleRateLimit();
        break;
      case 500:
        // 服务器错误 - 显示通用错误
        this.handleServerError(error.message);
        break;
      default:
        // 其他错误
        this.handleGenericError(error.message);
    }
  }

  private static handleUnauthorized(): void {
    // 清除本地token
    localStorage.removeItem('auth_token');

    // 通知Electron主进程清除认证状态
    window.electronAPI?.clearAuthState();

    // 跳转到登录页
    router.push('/login');

    // 显示提示
    toast.error('登录已过期，请重新登录');
  }

  private static handleRateLimit(): void {
    toast.warning('请求过于频繁，请稍后再试');
  }
}
```

## 7. 性能优化策略

### 7.1 大文件处理的内存管理

#### 7.1.1 流式处理策略

**上传内存优化**:
```typescript
class MemoryOptimizedUploader {
  private readonly CHUNK_SIZE = 20 * 1024 * 1024; // 20MB分片
  private readonly MAX_CONCURRENT_CHUNKS = 3;     // 最大并发分片数

  async uploadLargeFile(filePath: string): Promise<void> {
    const fileSize = await this.getFileSize(filePath);
    const totalChunks = Math.ceil(fileSize / this.CHUNK_SIZE);

    // 使用流式读取，避免将整个文件加载到内存
    const fileStream = fs.createReadStream(filePath, {
      highWaterMark: this.CHUNK_SIZE
    });

    let chunkIndex = 0;
    const activeUploads = new Set<Promise<void>>();

    for await (const chunk of fileStream) {
      // 控制并发数量
      if (activeUploads.size >= this.MAX_CONCURRENT_CHUNKS) {
        await Promise.race(activeUploads);
      }

      const uploadPromise = this.uploadChunk(chunk, chunkIndex++, totalChunks)
        .finally(() => activeUploads.delete(uploadPromise));

      activeUploads.add(uploadPromise);
    }

    // 等待所有分片上传完成
    await Promise.all(activeUploads);
  }

  private async uploadChunk(
    chunk: Buffer,
    index: number,
    total: number
  ): Promise<void> {
    // 上传完成后立即释放内存
    try {
      await this.tusClient.uploadChunk(chunk, index);
    } finally {
      // 显式释放chunk内存
      chunk.fill(0);
    }
  }
}
```

**下载内存优化**:
```typescript
class MemoryOptimizedDownloader {
  private readonly BUFFER_SIZE = 64 * 1024; // 64KB缓冲区

  async downloadLargeFile(url: string, savePath: string): Promise<void> {
    const response = await fetch(url);
    const reader = response.body?.getReader();
    const writer = fs.createWriteStream(savePath, {
      highWaterMark: this.BUFFER_SIZE
    });

    if (!reader) throw new Error('无法获取响应流');

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // 写入文件，避免内存积累
        await new Promise<void>((resolve, reject) => {
          writer.write(value, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });

        // 更新进度
        this.updateProgress(value.length);
      }
    } finally {
      reader.releaseLock();
      writer.end();
    }
  }
}
```

#### 7.1.2 垃圾回收优化

**主动内存管理**:
```typescript
class MemoryManager {
  private memoryUsageThreshold = 500 * 1024 * 1024; // 500MB阈值
  private gcInterval: NodeJS.Timeout | null = null;

  startMemoryMonitoring(): void {
    this.gcInterval = setInterval(() => {
      const memUsage = process.memoryUsage();

      if (memUsage.heapUsed > this.memoryUsageThreshold) {
        this.performGarbageCollection();
      }
    }, 30000); // 每30秒检查一次
  }

  private performGarbageCollection(): void {
    // 清理临时文件
    this.cleanupTempFiles();

    // 清理缓存
    this.clearCaches();

    // 强制垃圾回收（仅在开发环境）
    if (global.gc && process.env.NODE_ENV === 'development') {
      global.gc();
    }

    logger.info('内存清理完成', {
      before: this.memoryUsageThreshold,
      after: process.memoryUsage().heapUsed
    });
  }
}
```

### 7.2 并发控制和队列管理

#### 7.2.1 智能队列管理器

```typescript
class SmartQueueManager<T> {
  private queue: T[] = [];
  private processing = false;
  private concurrentLimit: number;
  private activeJobs = new Set<Promise<void>>();

  constructor(concurrentLimit: number = 5) {
    this.concurrentLimit = concurrentLimit;
  }

  async add(item: T, processor: (item: T) => Promise<void>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.queue.push({
        item,
        processor,
        resolve,
        reject
      } as any);

      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0 && this.activeJobs.size < this.concurrentLimit) {
      const job = this.queue.shift()!;
      const jobPromise = this.executeJob(job);

      this.activeJobs.add(jobPromise);
      jobPromise.finally(() => this.activeJobs.delete(jobPromise));
    }

    if (this.activeJobs.size === 0) {
      this.processing = false;
    } else {
      // 等待至少一个任务完成后继续处理队列
      await Promise.race(this.activeJobs);
      this.processing = false;
      this.processQueue();
    }
  }

  private async executeJob(job: any): Promise<void> {
    try {
      await job.processor(job.item);
      job.resolve();
    } catch (error) {
      job.reject(error);
    }
  }
}
```

#### 7.2.2 自适应并发控制

```typescript
class AdaptiveConcurrencyController {
  private currentConcurrency = 3;
  private readonly minConcurrency = 1;
  private readonly maxConcurrency = 10;
  private successCount = 0;
  private errorCount = 0;
  private lastAdjustment = Date.now();

  adjustConcurrency(success: boolean): void {
    if (success) {
      this.successCount++;
    } else {
      this.errorCount++;
    }

    // 每10个任务或每30秒调整一次
    const shouldAdjust =
      (this.successCount + this.errorCount) % 10 === 0 ||
      Date.now() - this.lastAdjustment > 30000;

    if (!shouldAdjust) return;

    const successRate = this.successCount / (this.successCount + this.errorCount);

    if (successRate > 0.9 && this.currentConcurrency < this.maxConcurrency) {
      // 成功率高，增加并发
      this.currentConcurrency = Math.min(
        this.currentConcurrency + 1,
        this.maxConcurrency
      );
    } else if (successRate < 0.7 && this.currentConcurrency > this.minConcurrency) {
      // 成功率低，减少并发
      this.currentConcurrency = Math.max(
        this.currentConcurrency - 1,
        this.minConcurrency
      );
    }

    this.resetCounters();
  }

  private resetCounters(): void {
    this.successCount = 0;
    this.errorCount = 0;
    this.lastAdjustment = Date.now();
  }
}
```

### 7.3 用户体验优化

#### 7.3.1 预加载和缓存策略

```typescript
class PreloadManager {
  private preloadCache = new Map<string, any>();
  private readonly CACHE_SIZE_LIMIT = 100;

  // 预加载下一页数据
  async preloadNextPage(currentPage: number, pageSize: number): Promise<void> {
    const nextPage = currentPage + 1;
    const cacheKey = `page_${nextPage}_${pageSize}`;

    if (this.preloadCache.has(cacheKey)) return;

    try {
      const data = await api.common.getDirectoryContents({
        page: nextPage,
        limit: pageSize
      });

      this.addToCache(cacheKey, data);
    } catch (error) {
      // 预加载失败不影响主流程
      console.warn('预加载失败:', error);
    }
  }

  // 智能缓存管理
  private addToCache(key: string, data: any): void {
    if (this.preloadCache.size >= this.CACHE_SIZE_LIMIT) {
      // LRU策略：删除最旧的缓存
      const firstKey = this.preloadCache.keys().next().value;
      this.preloadCache.delete(firstKey);
    }

    this.preloadCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
}
```

#### 7.3.2 渐进式加载

```typescript
// 图片懒加载指令
const vLazyLoad = {
  mounted(el: HTMLImageElement, binding: DirectiveBinding) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = binding.value;
          img.classList.remove('lazy-loading');
          observer.unobserve(img);
        }
      });
    });

    el.classList.add('lazy-loading');
    observer.observe(el);
  }
};

// 使用示例
<template>
  <img
    v-lazy-load="item.thumbnail"
    :alt="item.name"
    class="thumbnail"
  />
</template>
```

## 8. 开发和构建详解

### 8.1 开发环境设置

#### 8.1.1 环境要求
```bash
# Node.js版本要求
node >= 18.0.0

# pnpm版本要求
pnpm >= 8.0.0

# 系统要求
# Windows: Windows 10 或更高版本
# macOS: macOS 10.15 或更高版本
```

#### 8.1.2 开发环境初始化
```bash
# 1. 克隆项目
git clone <repository-url>
cd cloudDrive

# 2. 安装依赖
pnpm install

# 3. 复制环境配置文件
cp .env.development .env.local

# 4. 启动开发服务器
pnpm dev:electron

# 5. 类型检查（可选）
pnpm type-check

# 6. 代码规范检查（可选）
pnpm lint
```

#### 8.1.3 开发工具配置

**VSCode配置** (`.vscode/settings.json`):
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.vue": "vue"
  },
  "emmet.includeLanguages": {
    "vue": "html"
  }
}
```

**推荐扩展**:
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Tailwind CSS IntelliSense

### 8.2 构建流程详解

#### 8.2.1 开发构建
```bash
# 启动开发服务器（前端 + Electron）
pnpm dev:electron

# 仅启动前端开发服务器
pnpm dev

# 启动Electron（需要前端服务器运行）
pnpm electron
```

**开发构建特性**:
- 热模块替换 (HMR)
- 源码映射 (Source Maps)
- 开发者工具集成
- 实时类型检查

#### 8.2.2 生产构建
```bash
# 完整构建流程
pnpm build

# 分步构建
pnpm build:web      # 构建前端
pnpm build:electron # 构建Electron并打包
```

**构建优化**:
```typescript
// vite.config.ts 构建优化配置
export default defineConfig({
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['lucide-vue-next', 'radix-vue'],
          utils: ['axios', '@vueuse/core']
        }
      }
    },

    // 压缩配置
    minify: 'esbuild',

    // 源码映射（生产环境可选）
    sourcemap: false,

    // 构建目标
    target: 'esnext',

    // 资源内联阈值
    assetsInlineLimit: 4096
  }
});
```

### 8.3 环境配置管理

#### 8.3.1 环境变量配置

**开发环境** (`.env.development`):
```env
# 应用配置
VITE_APP_TITLE=CloudDrive Dev
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=10000

# 认证配置
VITE_AUTH_URL=http://test.cas.sh9130.com

# TUS上传配置
VITE_TUS_ENDPOINT=http://*************:8080/files
VITE_TUS_CHUNK_SIZE=20971520
VITE_TUS_PARALLEL_UPLOADS=5

# 下载配置
VITE_DOWNLOAD_ENDPOINT=http://*************:8000
VITE_DOWNLOAD_CHUNK_SIZE=20971520

# 智能打包配置
VITE_SMART_PACKING_THRESHOLD=10

# 功能开关
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK=false
```

**生产环境** (`.env.production`):
```env
# 应用配置
VITE_APP_TITLE=CloudDrive
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://************
VITE_API_TIMEOUT=10000

# 认证配置
VITE_AUTH_URL=https://cas.sh9130.com

# TUS上传配置
VITE_TUS_ENDPOINT=http://************:8080/files
VITE_TUS_CHUNK_SIZE=20971520
VITE_TUS_PARALLEL_UPLOADS=3

# 下载配置
VITE_DOWNLOAD_ENDPOINT=http://************
VITE_DOWNLOAD_CHUNK_SIZE=20971520

# 智能打包配置
VITE_SMART_PACKING_THRESHOLD=10

# 功能开关
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
```

#### 8.3.2 配置验证和类型安全

```typescript
// src/config/validation.ts
import { z } from 'zod';

const configSchema = z.object({
  app: z.object({
    title: z.string(),
    version: z.string()
  }),
  api: z.object({
    baseURL: z.string().url(),
    timeout: z.number().positive()
  }),
  tus: z.object({
    endpoint: z.string().url(),
    chunkSize: z.number().positive(),
    parallelUploads: z.number().positive().max(10)
  }),
  smartPacking: z.object({
    threshold: z.number().positive().min(1).max(1000) // 智能打包阈值：1-1000个文件
  }),
  auth: z.object({
    url: z.string().url()
  })
});

export function validateConfig(config: unknown): AppConfig {
  try {
    return configSchema.parse(config);
  } catch (error) {
    console.error('配置验证失败:', error);
    throw new Error('应用配置无效');
  }
}
```

## 9. 部署和打包详解

### 9.1 应用打包策略

#### 9.1.1 多平台构建配置

**Windows平台打包**:
```bash
# Windows x64 构建
pnpm build:win

# 构建流程
# 1. 前端构建: vue-tsc --noEmit && vite build
# 2. Electron构建: electron-builder --win --config electron-builder.yml
```

**macOS平台打包**:
```bash
# macOS ARM64 构建
pnpm build:mac

# 构建流程
# 1. 前端构建: vue-tsc --noEmit && vite build
# 2. Electron构建: electron-builder --mac --config electron-builder.yml
```

**测试版本构建**:
```bash
# 测试版Windows构建
pnpm build:test:win

# 测试版macOS构建
pnpm build:test:mac

# 使用测试配置: electron-builder.test.yml
```

#### 9.1.2 打包配置详解

**主配置文件** (`electron-builder.yml`):
```yaml
appId: com.clouddrive.app
productName: 星图

# 环境信息注入
extraMetadata:
  buildEnv: "production"
  isTestBuild: false

# 输出目录
directories:
  output: release

# 应用图标
icon: logo.png

# 额外资源文件
extraResources:
  - from: .env.development
    to: .env.development
  - from: .env.production
    to: .env.production
  - from: .env.test
    to: .env.test

# 包含的文件
files:
  - dist/**/*
  - dist-electron/**/*
  - "!node_modules/**/*"
  - "!src/**/*"
  - "!electron/**/*"

# Windows特定配置
win:
  icon: logo.png
  target:
    target: nsis
    arch:
      - x64
  # 代码签名配置（生产环境）
  certificateFile: "path/to/certificate.p12"
  certificatePassword: "${env.CERTIFICATE_PASSWORD}"

# macOS特定配置
mac:
  icon: logo.png
  target: dmg
  category: public.app-category.productivity
  # 代码签名配置
  identity: "Developer ID Application: Your Name"
  hardenedRuntime: true
  entitlements: "build/entitlements.mac.plist"

# NSIS安装程序配置
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: "星图"

# 发布配置（自动更新）
publish:
  provider: generic
  url: "https://releases.example.com/"
```

**测试配置文件** (`electron-builder.test.yml`):
```yaml
appId: com.clouddrive.app.test
productName: 星图 测试版

extraMetadata:
  buildEnv: "test"
  isTestBuild: true

directories:
  output: release-test

# 测试版发布配置
publish:
  provider: generic
  url: "http://*************:8000/packages/update-files/"
```

#### 9.1.3 构建优化策略

**资源优化**:
```typescript
// 构建时资源优化
const buildOptimization = {
  // 排除开发依赖
  excludeDevDependencies: true,

  // 压缩配置
  compression: {
    level: 9,        // 最高压缩级别
    algorithm: 'gzip'
  },

  // 资源内联
  inlineAssets: {
    limit: 8192,     // 8KB以下资源内联
    types: ['png', 'jpg', 'svg', 'woff2']
  },

  // 代码分割
  codeSplitting: {
    vendor: ['vue', 'vue-router', 'pinia'],
    ui: ['lucide-vue-next', 'radix-vue'],
    electron: ['electron', 'electron-log', 'electron-store']
  }
};
```

### 9.2 自动更新系统

#### 9.2.1 更新检查机制

```typescript
// electron/auto-updater/updateManager.ts
class UpdateManager {
  private autoUpdater: AppUpdater;
  private updateCheckInterval: NodeJS.Timeout | null = null;

  constructor(private mainWindow: BrowserWindow) {
    this.autoUpdater = new AppUpdater();
    this.setupUpdateHandlers();
  }

  async initialize(): Promise<void> {
    // 配置更新服务器
    this.autoUpdater.setFeedURL({
      provider: 'generic',
      url: process.env.UPDATE_SERVER_URL || 'https://releases.example.com/'
    });

    // 启动定期检查
    this.startPeriodicCheck();

    // 应用启动时检查更新
    setTimeout(() => {
      this.checkForUpdates();
    }, 5000); // 延迟5秒，让应用完全启动
  }

  private setupUpdateHandlers(): void {
    // 发现可用更新
    this.autoUpdater.on('update-available', (info) => {
      logger.info('发现可用更新:', info);
      this.mainWindow.webContents.send('auto-updater:update-available', info);
    });

    // 没有可用更新
    this.autoUpdater.on('update-not-available', (info) => {
      logger.info('当前已是最新版本:', info);
    });

    // 下载进度
    this.autoUpdater.on('download-progress', (progress) => {
      this.mainWindow.webContents.send('auto-updater:download-progress', progress);
    });

    // 下载完成
    this.autoUpdater.on('update-downloaded', (info) => {
      logger.info('更新下载完成:', info);
      this.mainWindow.webContents.send('auto-updater:update-downloaded', info);
    });

    // 更新错误
    this.autoUpdater.on('error', (error) => {
      logger.error('自动更新错误:', error);
      this.mainWindow.webContents.send('auto-updater:error', error.message);
    });
  }

  private startPeriodicCheck(): void {
    // 每4小时检查一次更新
    this.updateCheckInterval = setInterval(() => {
      this.checkForUpdates();
    }, 4 * 60 * 60 * 1000);
  }

  async checkForUpdates(): Promise<void> {
    try {
      await this.autoUpdater.checkForUpdatesAndNotify();
    } catch (error) {
      logger.error('检查更新失败:', error);
    }
  }

  async downloadUpdate(): Promise<void> {
    try {
      await this.autoUpdater.downloadUpdate();
    } catch (error) {
      logger.error('下载更新失败:', error);
      throw error;
    }
  }

  quitAndInstall(): void {
    this.autoUpdater.quitAndInstall();
  }
}
```

#### 9.2.2 增量更新实现

**更新包生成**:
```typescript
// 构建脚本中的增量更新配置
const incrementalUpdateConfig = {
  // 启用增量更新
  differentialDownload: true,

  // 更新包配置
  updateInfo: {
    // 版本信息
    version: packageJson.version,

    // 更新日志
    releaseNotes: await generateReleaseNotes(),

    // 文件校验
    files: [
      {
        url: "StarMap-Setup-1.0.1.exe",
        sha512: "...",
        size: 85264896
      }
    ],

    // 增量更新包
    packages: {
      "1.0.0": {
        url: "StarMap-1.0.0-to-1.0.1.nupkg",
        sha512: "...",
        size: 12345678
      }
    }
  }
};
```

#### 9.2.3 更新UI组件

```vue
<!-- src/components/AutoUpdater/UpdateNotification.vue -->
<template>
  <div v-if="updateAvailable" class="update-notification">
    <div class="update-content">
      <div class="update-header">
        <Download class="update-icon" />
        <h3>发现新版本</h3>
      </div>

      <div class="update-info">
        <p>版本 {{ updateInfo.version }} 已可用</p>
        <div v-if="updateInfo.releaseNotes" class="release-notes">
          <details>
            <summary>查看更新内容</summary>
            <div v-html="updateInfo.releaseNotes"></div>
          </details>
        </div>
      </div>

      <div class="update-progress" v-if="downloading">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: downloadProgress + '%' }"
          ></div>
        </div>
        <p>下载进度: {{ downloadProgress.toFixed(1) }}%</p>
      </div>

      <div class="update-actions">
        <Button
          v-if="!downloading && !downloaded"
          @click="startDownload"
          class="primary"
        >
          立即更新
        </Button>

        <Button
          v-if="downloaded"
          @click="installUpdate"
          class="primary"
        >
          重启并安装
        </Button>

        <Button
          @click="dismissUpdate"
          variant="outline"
        >
          稍后提醒
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Download } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

interface UpdateInfo {
  version: string;
  releaseNotes?: string;
  releaseDate: string;
}

const updateAvailable = ref(false);
const downloading = ref(false);
const downloaded = ref(false);
const downloadProgress = ref(0);
const updateInfo = ref<UpdateInfo | null>(null);

const startDownload = async () => {
  downloading.value = true;
  try {
    await window.electronAPI.autoUpdater.downloadUpdate();
  } catch (error) {
    console.error('下载更新失败:', error);
    downloading.value = false;
  }
};

const installUpdate = () => {
  window.electronAPI.autoUpdater.quitAndInstall();
};

const dismissUpdate = () => {
  updateAvailable.value = false;
  // 设置稍后提醒（例如1小时后）
  setTimeout(() => {
    updateAvailable.value = true;
  }, 60 * 60 * 1000);
};

// 监听更新事件
onMounted(() => {
  window.electronAPI.autoUpdater.onUpdateAvailable((info: UpdateInfo) => {
    updateInfo.value = info;
    updateAvailable.value = true;
  });

  window.electronAPI.autoUpdater.onDownloadProgress((progress: number) => {
    downloadProgress.value = progress;
  });

  window.electronAPI.autoUpdater.onUpdateDownloaded(() => {
    downloading.value = false;
    downloaded.value = true;
  });
});
</script>
```

### 9.3 分发和部署策略

#### 9.3.1 发布流程自动化

**GitHub Actions配置** (`.github/workflows/build.yml`):
```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Install dependencies
      run: pnpm install

    - name: Build application
      run: pnpm build
      env:
        CERTIFICATE_PASSWORD: ${{ secrets.CERTIFICATE_PASSWORD }}

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-build
        path: release/
```

#### 9.3.2 更新服务器配置

**更新服务器实现** (Node.js + Express):
```typescript
// update-server/server.ts
import express from 'express';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const app = express();
const RELEASES_DIR = './releases';

// 获取最新版本信息
app.get('/latest', (req, res) => {
  const platform = req.query.platform as string;
  const currentVersion = req.query.version as string;

  try {
    const latestInfo = getLatestVersionInfo(platform);

    if (isNewerVersion(latestInfo.version, currentVersion)) {
      res.json(latestInfo);
    } else {
      res.status(204).send(); // No Content - 无更新
    }
  } catch (error) {
    res.status(500).json({ error: 'Failed to check for updates' });
  }
});

// 下载更新包
app.get('/download/:platform/:filename', (req, res) => {
  const { platform, filename } = req.params;
  const filePath = join(RELEASES_DIR, platform, filename);

  if (existsSync(filePath)) {
    res.download(filePath);
  } else {
    res.status(404).json({ error: 'File not found' });
  }
});

function getLatestVersionInfo(platform: string) {
  const infoPath = join(RELEASES_DIR, platform, 'latest.yml');
  const yamlContent = readFileSync(infoPath, 'utf8');
  return parseYaml(yamlContent);
}

function isNewerVersion(latest: string, current: string): boolean {
  const latestParts = latest.split('.').map(Number);
  const currentParts = current.split('.').map(Number);

  for (let i = 0; i < Math.max(latestParts.length, currentParts.length); i++) {
    const latestPart = latestParts[i] || 0;
    const currentPart = currentParts[i] || 0;

    if (latestPart > currentPart) return true;
    if (latestPart < currentPart) return false;
  }

  return false;
}

app.listen(3000, () => {
  console.log('更新服务器启动在端口 3000');
});
```

#### 9.3.3 安全和签名

**代码签名配置**:
```bash
# Windows代码签名
signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com StarMap-Setup.exe

# macOS代码签名
codesign --force --verify --verbose --sign "Developer ID Application: Your Name" StarMap.app
```

**安全检查清单**:
- [ ] 代码签名证书配置
- [ ] 更新包完整性校验
- [ ] HTTPS传输加密
- [ ] 版本回滚机制
- [ ] 恶意软件扫描
- [ ] 用户权限最小化

---

## 总结

本技术文档详细介绍了星图(CloudDrive)项目的完整技术架构和实现细节。项目采用现代化的技术栈，通过模块化设计实现了高性能的文件管理、上传下载、压缩解压等核心功能。

### 技术亮点

1. **TUS协议断点续传**: 可靠的大文件上传解决方案
2. **流式下载**: 突破内存限制的大文件下载
3. **智能压缩**: 自动检测和7z快速压缩（10个文件阈值，压缩级别0）
4. **模块化架构**: 清晰的代码组织和职责分离
5. **性能优化**: 内存管理、并发控制、用户体验优化
6. **自动更新**: 完整的版本管理和增量更新系统

### 配置优化策略

项目采用了针对用户体验优化的配置策略：

- **智能打包阈值**: 10个文件即触发，更早优化网络请求
- **压缩级别**: 0级压缩（最快速度），减少用户等待时间
- **设计理念**: 在大量小文件场景下，减少HTTP请求数量带来的性能提升远大于高压缩率的传输优化

### 开发建议

- 遵循TypeScript类型安全原则
- 使用组合式API进行逻辑复用
- 重视性能优化和用户体验
- 保持代码的可测试性和可维护性
- 及时更新依赖和安全补丁

*本文档将随着项目发展持续更新和完善。*
